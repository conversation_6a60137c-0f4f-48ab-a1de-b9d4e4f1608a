# AUGMENT AGENT TOOL DESCRIPTIONS - DETAILED REFERENCE

## 1. str-replace-editor
**Purpose**: Tool for editing files with precise string replacement and insertion capabilities.

**Key Features**:
- `path`: File path relative to workspace root
- `insert` and `str_replace` commands with snippet output showing final state after edits
- Must generate `instruction_reminder` to limit edits to at most 150 lines

**str_replace Command Parameters**:
- `old_str_N`: Exact string to replace (must match consecutive lines exactly, including whitespace)
- `new_str_N`: Replacement string (can be empty to delete content)
- `old_str_start_line_number_N`: 1-based inclusive start line number
- `old_str_end_line_number_N`: 1-based inclusive end line number
- Supports multiple replacements in one call (old_str_1, old_str_2, etc.)
- Line numbers must not overlap between replacements

**insert Command Parameters**:
- `insert_line_N`: Line number after which to insert (1-based, use 0 for beginning)
- `new_str_N`: String to insert
- Supports multiple insertions in one call

**Important Constraints**:
- ONLY tool for editing files - do not use sed or other command line tools
- Do not remove and recreate files from scratch
- Use view tool to read files before editing
- Try to fit multiple edits in one tool call

## 2. open-browser
**Purpose**: Open URLs in the default browser for user inspection.

**Behavior**:
- Takes URL and opens in default browser
- No content returned - intended for visual inspection
- Jumps user to browser window
- Do NOT reuse URLs already opened in conversation (annoying to user)

## 3. diagnostics
**Purpose**: Get IDE issues (errors, warnings, etc.) for specific files.

**Parameters**:
- `paths`: Required array of file paths to check for issues

## 4. read-terminal
**Purpose**: Read output from active or most recent VSCode terminal.

**Options**:
- Default: Reads all visible terminal text
- `only_selected=true`: Read only selected text (use only when user has selected text)
- Unrelated to launch-process tools

## 5. git-commit-retrieval
**Purpose**: Augment's context engine with git commit history awareness.

**Functionality**:
1. Takes natural language description of code you're looking for
2. Uses git commit history as context for retrieval
3. Functions like standard codebase-retrieval but with historical context

## 6. launch-process
**Purpose**: Launch shell commands as waiting or background processes.

**Wait Modes**:
- `wait=true`: Interactive terminal, waits up to `max_wait_seconds`
- `wait=false`: Background process, returns immediately

**Parameters**:
- `command`: Shell command to execute
- `wait`: Boolean for wait mode
- `max_wait_seconds`: Timeout for waiting processes
- `cwd`: Required absolute path to working directory

**Usage Guidelines**:
- Use `wait=true` for short commands or when you need completion before proceeding
- Use `wait=false` for servers or long-running background processes
- Only one waiting process can run at a time
- Can interact with processes using other process tools
- OS: darwin, Shell: bash

## 7. kill-process
**Purpose**: Terminate a process by its terminal ID.

**Parameters**:
- `terminal_id`: Terminal ID to kill

## 8. read-process
**Purpose**: Read output from a specific terminal.

**Parameters**:
- `terminal_id`: Terminal ID to read from
- `wait`: Whether to wait for completion
- `max_wait_seconds`: Wait timeout (only relevant when wait=true)

**Behavior**:
- If `wait=true` and process not complete: waits up to timeout
- If `wait=false` or process complete: returns immediately

## 9. write-process
**Purpose**: Send input to a running terminal process.

**Parameters**:
- `terminal_id`: Terminal ID to write to
- `input_text`: Text to send to process stdin

## 10. list-processes
**Purpose**: List all terminals created with launch-process tool and their states.

**Returns**: Information about all known terminals and their current states

## 11. web-search
**Purpose**: Search the web using Google's Custom Search API.

**Parameters**:
- `query`: Search query string
- `num_results`: Number of results (1-10, default: 5)

**Returns**: Results in markdown format with URL, title, and snippets

## 12. web-fetch
**Purpose**: Fetch webpage content and convert to Markdown.

**Parameters**:
- `url`: URL to fetch

**Returns**: Page content in Markdown format (invalid Markdown indicates parsing failure)

## 13. codebase-retrieval
**Purpose**: Augment's world-class codebase context engine.

**Capabilities**:
1. Takes natural language description of code you're looking for
2. Uses proprietary retrieval/embedding models for highest-quality recall
3. Maintains real-time codebase index (always up-to-date)
4. Multi-language support
5. Only reflects current disk state (no version control history)

## 14. remove-files
**Purpose**: Safely delete files in user's workspace.

**Parameters**:
- `file_paths`: Array of file paths to remove

**Important**: ONLY safe tool for file deletion - do NOT use shell/launch-process for deletion

## 15. save-file
**Purpose**: Create new files with content.

**Parameters**:
- `path`: File path to save
- `file_content`: Content of the file
- `instructions_reminder`: Must be exact string about 300-line limit
- `add_last_line_newline`: Whether to add newline at end (default: true)

**Constraints**:
- CANNOT modify existing files
- Use str-replace-editor for editing existing files
- Generate instructions_reminder first
- Limit content to 300 lines max

## 16. view_tasklist
**Purpose**: View the current task list structure for the conversation.

**Returns**: Current task list in organized format showing task hierarchy and states

## 17. reorganize_tasklist
**Purpose**: Major restructuring of task list (reordering, changing hierarchy).

**Parameters**:
- `markdown`: Markdown representation of task list structure
- New tasks should have UUID 'NEW_UUID'
- Must contain exactly one root task with proper dash indentation hierarchy

**Usage**: Only for major restructuring - use update_tasks for individual task updates

## 18. update_tasks
**Purpose**: Update properties of one or more existing tasks.

**Parameters**:
- `tasks`: Array of task objects to update
- Each task object can have: `task_id` (required), `state`, `name`, `description`

**Task States**:
- `NOT_STARTED`: [ ] - Tasks not yet begun
- `IN_PROGRESS`: [/] - Currently working on
- `CANCELLED`: [-] - No longer relevant
- `COMPLETE`: [x] - Finished tasks

**Best Practices**:
- Use batch updates for efficiency
- When starting new task: mark previous complete and new task in progress in single call
- Use for complex work planning, progress tracking, and work management

## 19. add_tasks
**Purpose**: Create new tasks or subtasks in the task list.

**Parameters**:
- `tasks`: Array of new task objects
- Each task needs: `name` (required), `description` (required)
- Optional: `parent_task_id` (for subtasks), `after_task_id` (insertion point), `state`

**Usage**: For planning complex sequences of work with structured task hierarchy

## 20. remember
**Purpose**: Store long-term memories for future reference.

**Parameters**:
- `memory`: Concise (1 sentence) memory to store

**Usage Guidelines**:
- Only for information useful long-term
- Do NOT use for temporary information
- Call when user asks to remember something or create memories

## 21. render-mermaid
**Purpose**: Render interactive Mermaid diagrams from code definitions.

**Parameters**:
- `diagram_definition`: Mermaid diagram code to render
- `title`: Optional diagram title (default: "Mermaid Diagram")

**Features**: Interactive diagram with pan/zoom controls and copy functionality

## 22. view-range-untruncated
**Purpose**: View specific line ranges from truncated content.

**Parameters**:
- `reference_id`: Reference ID from truncation footer
- `start_line`: Starting line number (1-based, inclusive)
- `end_line`: Ending line number (1-based, inclusive)

## 23. search-untruncated
**Purpose**: Search for terms within truncated content.

**Parameters**:
- `reference_id`: Reference ID from truncation footer
- `search_term`: Term to search for
- `context_lines`: Lines of context before/after matches (default: 2)

## 24. view
**Purpose**: Advanced file/directory viewing with regex search capabilities.

**Parameters**:
- `path`: File or directory path relative to workspace root
- `type`: "file" or "directory"
- `search_query_regex`: Optional regex pattern for file search
- `case_sensitive`: Case sensitivity for regex (default: false)
- `context_lines_before/after`: Context lines around matches (default: 5)
- `view_range`: Line range [start, end] for files (1-based, inclusive)

**File Behavior**:
- Displays `cat -n` output
- Long output truncated with `<response clipped>`
- Regex search shows only matching lines + context
- Non-matching sections replaced with `...`

**Directory Behavior**:
- Lists files and subdirectories up to 2 levels deep

**Regex Syntax Support**:
- Escaping: `\.` `\+` `\?` `\*` `\|` `\(` `\)` `\[`
- Dot `.`: matches any character except newline
- Character classes: `[abc]`, `[a-z]`, `[^...]`
- Alternation: `foo|bar`
- Quantifiers: `*`, `+`, `?`, `{n}`, `{n,}`, `{n,m}` (add `?` for lazy)
- Anchors: `^` (start of line), `$` (end of line)
- Special: `\t` for tab

**Regex Limitations (Unsupported)**:
- Newline `\n` (single line mode only)
- Look-ahead/behind `(?=...)`, `(?<=...)`
- Back-references `\1`, `\k<name>`
- Named groups `(?<name>...)`, `(?P<name>...)`
- Shorthand classes `\d`, `\s`, `\w`, `\b`
- Unicode property escapes `\p{...}`
- Inline flags `(?i)`, `(?m)`
- Advanced features: recursion, conditionals, atomic groups, possessive quantifiers
- Unicode escapes `\u{1F60A}`, `\u1F60A`

**Usage Guidelines**:
- Prefer `search_query_regex` over `view_range` for finding specific symbols
- Use `[start_line, -1]` to show from start_line to end of file
- Strongly recommended to search rather than view large ranges

---

# TOOL USAGE PHILOSOPHY & BEST PRACTICES

## Information Gathering Priority
1. **Always gather context first** using codebase-retrieval before making edits
2. Ask for ALL symbols involved in edits at extremely detailed level
3. Use git-commit-retrieval to understand how similar changes were made previously
4. Single comprehensive call rather than multiple small calls

## Task Management Strategy
- Use for complex multi-step work requiring structured planning
- Each subtask should represent ~20 minutes of professional developer work
- Batch update task states efficiently
- Mark previous task complete and new task in progress in single call

## Package Management Approach
- **Always use package managers** (npm, pip, cargo, etc.) instead of manual file editing
- Prevents version conflicts and dependency resolution issues
- Only edit package files directly for complex configurations impossible via package managers

## Code Display Standards
- **Always wrap code in `<augment_code_snippet>` tags** with `path=` and `mode="EXCERPT"`
- Use four backticks (````) instead of three
- Keep excerpts brief (<10 lines) - users can click for full context
- Never use regular markdown code blocks for existing code

## Conservative Development Approach
- Focus strictly on user requirements - do NOT exceed scope
- Ask before potentially damaging actions (commits, deployments, dependency changes)
- Suggest testing after code changes
- If going in circles, ask user for help

## Error Recovery
- If str-replace-editor fails, fix inputs and retry
- Do NOT fall back to recreating files from scratch
- Use view tool to understand file state before editing
- Fit multiple edits in single tool call when possible

This comprehensive tool suite enables sophisticated software development assistance from planning through implementation, testing, and deployment, with emphasis on context-aware decision making and structured project management.
